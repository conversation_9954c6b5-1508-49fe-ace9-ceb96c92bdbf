<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/fd894b6b769b40d1.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-005f611b6ba08cc8.js"/><script src="/_next/static/chunks/4bd1b696-3d64c145d25aeb52.js" async=""></script><script src="/_next/static/chunks/1684-5f16156a9576207b.js" async=""></script><script src="/_next/static/chunks/main-app-a1f9594c7aa9a008.js" async=""></script><script src="/_next/static/chunks/2108-01f45b4bd0af2163.js" async=""></script><script src="/_next/static/chunks/1603-e114a4e36a8b775d.js" async=""></script><script src="/_next/static/chunks/app/layout-617fbcf76a0e5f3c.js" async=""></script><script src="/_next/static/chunks/5003-f1c951693ce1da97.js" async=""></script><script src="/_next/static/chunks/6221-af8dd20f8eba536a.js" async=""></script><script src="/_next/static/chunks/6874-1571340a9900ccc2.js" async=""></script><script src="/_next/static/chunks/9526-48bf5e0dc984c543.js" async=""></script><script src="/_next/static/chunks/2198-a256aef0768b8d69.js" async=""></script><script src="/_next/static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js" async=""></script><script src="/_next/static/chunks/4358-5cb69af792433a85.js" async=""></script><script src="/_next/static/chunks/5027-f5e2ba4025e01bdb.js" async=""></script><script src="/_next/static/chunks/app/(dashboard)/dashboard/communication/page-4b4b74e909f10e42.js" async=""></script><title>Innovative Centre CRM</title><meta name="description" content="Customer Relationship Management System for Innovative Centre"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50"><div class="flex flex-col w-72 bg-white shadow-xl h-full border-r border-gray-100"><div class="flex items-center justify-center h-20 px-6 gradient-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-8 w-8 text-white mr-3"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg><span class="text-xl font-bold text-white tracking-tight">Innovative CRM</span></div><nav class="flex-1 px-6 py-6 space-y-2 overflow-y-auto"></nav></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100"><div class="flex items-center justify-between px-8 py-5"><div class="flex items-center flex-1 gap-6"><div class="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2 h-4 w-4"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path><path d="M10 6h4"></path><path d="M10 10h4"></path><path d="M10 14h4"></path><path d="M10 18h4"></path></svg><span class="text-sm">Loading...</span></div><div class="relative max-w-lg w-full"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><input type="text" placeholder="Search students, leads, groups..." class="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white transition-all duration-200 text-sm font-medium"/></div></div><div class="flex items-center space-x-4"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md hover:bg-accent hover:text-accent-foreground h-10 w-10 relative" type="button" id="radix-«R36nb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg></button><div class="relative"><button class="justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md hover:text-accent-foreground h-10 flex items-center space-x-3 hover:bg-gray-100 rounded-xl px-4 py-2"><div class="h-8 w-8 rounded-full gradient-primary flex items-center justify-center"><span class="text-sm font-medium text-white">U</span></div><span class="hidden md:block font-medium text-gray-700">User</span></button></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8"><div class="max-w-7xl mx-auto"><div class="fade-in"><div class="space-y-6"><div class="flex justify-between items-center"><div><h1 class="text-3xl font-bold text-gray-900">Communication Center</h1><p class="text-gray-600">Send messages, manage workflows, and configure notifications</p></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-4"><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-8 w-8 text-blue-600"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">SMS Sent Today</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-8 w-8 text-green-600"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Calls Made</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-8 w-8 text-purple-600"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Emails Sent</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-8 w-8 text-orange-600"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Active Notifications</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div></div></div><div dir="ltr" data-orientation="horizontal" class="space-y-6"><div role="tablist" aria-orientation="horizontal" class="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«Rgutqnb»-content-send" data-state="active" id="radix-«Rgutqnb»-trigger-send" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Send Messages</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Rgutqnb»-content-workflows" data-state="inactive" id="radix-«Rgutqnb»-trigger-workflows" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Workflows</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Rgutqnb»-content-settings" data-state="inactive" id="radix-«Rgutqnb»-trigger-settings" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Settings</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rgutqnb»-trigger-send" id="radix-«Rgutqnb»-content-send" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" style="animation-duration:0s"><div class="grid grid-cols-1 lg:grid-cols-3 gap-6"><div class="lg:col-span-2"><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Send Message</h3><p class="text-sm text-muted-foreground">Compose and send messages to students or groups</p></div><div class="p-6 pt-0 space-y-4"><div class="flex space-x-2"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02] h-8 rounded-lg px-3 text-xs">SMS</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs">WHATSAPP</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs">EMAIL</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs">NOTIFICATION</button></div><div class="space-y-2"><label class="text-sm font-medium">Recipient</label><button type="button" role="combobox" aria-controls="radix-«Riilgutqnb»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" data-placeholder="" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;&gt;span]:line-clamp-1"><span style="pointer-events:none">Select recipient</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 opacity-50" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></div><div class="space-y-2"><label class="text-sm font-medium">Message</label><textarea class="w-full p-3 border border-gray-300 rounded-md resize-none" rows="4" placeholder="Type your message here..."></textarea><div class="text-sm text-gray-500">0<!-- -->/160 characters</div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><input type="checkbox" id="schedule" class="rounded"/><label for="schedule" class="text-sm">Schedule for later</label></div><button class="justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02] h-10 px-4 py-2 flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send h-4 w-4"><path d="m22 2-7 20-4-9-9-4Z"></path><path d="M22 2 11 13"></path></svg><span>Send Message</span></button></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm mt-6"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Messages</h3><p class="text-sm text-muted-foreground">Latest sent messages and their status</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="flex items-start space-x-3 p-3 border rounded-lg"><div class="flex-shrink-0"><div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-blue-600"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-gray-900">Aziza Karimova</p><div class="flex items-center space-x-2"><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs">SMS</div><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-green-100 text-green-800">Delivered</div></div></div><p class="text-sm text-gray-600 mt-1">Reminder: Your IELTS class starts tomorrow at 10:00 AM</p><p class="text-xs text-gray-400 mt-1">2 hours ago</p></div></div><div class="flex items-start space-x-3 p-3 border rounded-lg"><div class="flex-shrink-0"><div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-blue-600"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-gray-900">Bobur Toshev</p><div class="flex items-center space-x-2"><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs">WhatsApp</div><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-blue-100 text-blue-800">Read</div></div></div><p class="text-sm text-gray-600 mt-1">Your payment has been received. Thank you!</p><p class="text-xs text-gray-400 mt-1">4 hours ago</p></div></div><div class="flex items-start space-x-3 p-3 border rounded-lg"><div class="flex-shrink-0"><div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-blue-600"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-gray-900">Group A1-Morning</p><div class="flex items-center space-x-2"><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs">SMS</div><div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-green-100 text-green-800">Delivered</div></div></div><p class="text-sm text-gray-600 mt-1">Class moved to Room 205 today</p><p class="text-xs text-gray-400 mt-1">6 hours ago</p></div></div></div></div></div></div><div class="space-y-6"><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Message Templates</h3><p class="text-sm text-muted-foreground">Quick templates for common messages</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"><h4 class="font-medium text-sm">Class Reminder</h4><p class="text-xs text-gray-600 mt-1">Reminder: Your {course} class starts {time} at {location}</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs mt-2">Use Template</button></div><div class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"><h4 class="font-medium text-sm">Payment Confirmation</h4><p class="text-xs text-gray-600 mt-1">Your payment of {amount} has been received. Thank you!</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs mt-2">Use Template</button></div><div class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"><h4 class="font-medium text-sm">Welcome Message</h4><p class="text-xs text-gray-600 mt-1">Welcome to Innovative Centre! Your journey to English mastery begins now.</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs mt-2">Use Template</button></div><div class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"><h4 class="font-medium text-sm">Assignment Reminder</h4><p class="text-xs text-gray-600 mt-1">Don&#x27;t forget to complete your homework for tomorrow&#x27;s class.</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs mt-2">Use Template</button></div></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Quick Actions</h3><p class="text-sm text-muted-foreground">Common communication tasks</p></div><div class="p-6 pt-0"><div class="space-y-3"><button class="inline-flex items-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-10 px-4 py-2 w-full justify-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-4 w-4 mr-2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>Send Class Reminder</button><button class="inline-flex items-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-10 px-4 py-2 w-full justify-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-4 w-4 mr-2"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg>Payment Reminder</button><button class="inline-flex items-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-10 px-4 py-2 w-full justify-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 mr-2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>Welcome New Students</button><button class="inline-flex items-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-10 px-4 py-2 w-full justify-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-4 w-4 mr-2"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>Schedule Follow-up</button></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Settings</h3><p class="text-sm text-muted-foreground">Communication preferences</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm">SMS Notifications</span><input type="checkbox" class="rounded" checked=""/></div><div class="flex items-center justify-between"><span class="text-sm">Email Notifications</span><input type="checkbox" class="rounded" checked=""/></div><div class="flex items-center justify-between"><span class="text-sm">WhatsApp Integration</span><input type="checkbox" class="rounded" checked=""/></div><div class="flex items-center justify-between"><span class="text-sm">Auto Reminders</span><input type="checkbox" class="rounded" checked=""/></div></div></div></div></div></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rgutqnb»-trigger-workflows" hidden="" id="radix-«Rgutqnb»-content-workflows" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rgutqnb»-trigger-settings" hidden="" id="radix-«Rgutqnb»-content-settings" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div></div></main></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div></div><script src="/_next/static/chunks/webpack-005f611b6ba08cc8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[2044,[\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"1603\",\"static/chunks/1603-e114a4e36a8b775d.js\",\"7177\",\"static/chunks/app/layout-617fbcf76a0e5f3c.js\"],\"QueryProvider\"]\n3:I[8230,[\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"1603\",\"static/chunks/1603-e114a4e36a8b775d.js\",\"7177\",\"static/chunks/app/layout-617fbcf76a0e5f3c.js\"],\"AuthProvider\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n6:I[7705,[\"5003\",\"static/chunks/5003-f1c951693ce1da97.js\",\"6221\",\"static/chunks/6221-af8dd20f8eba536a.js\",\"6874\",\"static/chunks/6874-1571340a9900ccc2.js\",\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"9526\",\"static/chunks/9526-48bf5e0dc984c543.js\",\"2198\",\"static/chunks/2198-a256aef0768b8d69.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js\"],\"BranchProvider\"]\n7:I[5248,[\"5003\",\"static/chunks/5003-f1c951693ce1da97.js\",\"6221\",\"static/chunks/6221-af8dd20f8eba536a.js\",\"6874\",\"static/chunks/6874-1571340a9900ccc2.js\",\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"9526\",\"static/chunks/9526-48bf5e0dc984c543.js\",\"2198\",\"static/chunks/2198-a256aef0768b8d69.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js\"],\"Sidebar\"]\n8:I[2871,[\"5003\",\"static/chunks/5003-f1c951693ce1da97.js\",\"6221\",\"static/chunks/6221-af8dd20f8eba536a.js\",\"6874\",\"static/chunks/6874-1571340a9900ccc2.js\",\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"9526\",\"static/chunks/9526-48bf5e0dc984c543.js\",\"2198\",\"static/chunks/2198-a256aef0768b8d69.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js\"],\"Header\"]\n9:I[9800,[\"5003\",\"static/chunks/5003-f1c951693ce1da97.js\",\"6221\",\"static/chunks/6221-af8dd20f8eba536a.js\",\"6874\",\"static/chunks/6874-1571340a9900ccc2.js\",\"2108\",\"static/chunks/2108-01f45b4bd0af2163.js\",\"9526\",\"static/chunks/9526-48bf5e0dc984c543.js\",\"2198\",\"static/chunks/2198-a256aef0768b8d69.js\",\"9305\",\"static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js\"],\"Toaster\"]\na:I[894,[],\"ClientPageRoot\"]\nb:I[7189,[\"5003\",\"static/chunks/5003-f1c951693ce1da97.js\",\"6221\",\"static/chunks/6221-af8dd20f8eba536a"])</script><script>self.__next_f.push([1,".js\",\"4358\",\"static/chunks/4358-5cb69af792433a85.js\",\"5027\",\"static/chunks/5027-f5e2ba4025e01bdb.js\",\"8750\",\"static/chunks/app/(dashboard)/dashboard/communication/page-4b4b74e909f10e42.js\"],\"default\"]\ne:I[9665,[],\"MetadataBoundary\"]\n10:I[9665,[],\"OutletBoundary\"]\n13:I[4911,[],\"AsyncMetadataOutlet\"]\n15:I[9665,[],\"ViewportBoundary\"]\n17:I[6614,[],\"\"]\n:HL[\"/_next/static/css/fd894b6b769b40d1.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"_nuDRKSdZmRp0XeTsFzUr\",\"p\":\"\",\"c\":[\"\",\"dashboard\",\"communication\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(dashboard)\",{\"children\":[\"dashboard\",{\"children\":[\"communication\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/fd894b6b769b40d1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]}]]}],{\"children\":[\"(dashboard)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L6\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\"children\":[[\"$\",\"$L7\",null,{}],[\"$\",\"div\",null,{\"className\":\"flex-1 flex flex-col overflow-hidden\",\"children\":[[\"$\",\"$L8\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto\",\"children\":[\"$\",\"div\",null,{\"className\":\"fade-in\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],[\"$\",\"$L9\",null,{}]]}]}]]}],{\"children\":[\"dashboard\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"communication\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$La\",null,{\"Component\":\"$b\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@c\",\"$@d\"]}],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null,[\"$\",\"$L10\",null,{\"children\":[\"$L11\",\"$L12\",[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"wldoJ5HaknwNqhbYzWHH1\",{\"children\":[[\"$\",\"$L15\",null,{\"children\":\"$L16\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$17\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"18:\"$Sreact.suspense\"\n19:I[4911,[],\"AsyncMetadata\"]\nc:{}\nd:{}\nf:[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[\"$\",\"$L19\",null,{\"promise\":\"$@1a\"}]}]\n"])</script><script>self.__next_f.push([1,"12:null\n"])</script><script>self.__next_f.push([1,"16:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n11:null\n"])</script><script>self.__next_f.push([1,"1a:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Innovative Centre CRM\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Customer Relationship Management System for Innovative Centre\"}]],\"error\":null,\"digest\":\"$undefined\"}\n14:{\"metadata\":\"$1a:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>