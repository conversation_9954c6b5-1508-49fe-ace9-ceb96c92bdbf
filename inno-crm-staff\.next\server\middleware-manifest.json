{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "_nuDRKSdZmRp0XeTsFzUr", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ch1cDn+BG5IYPLdK2u8ZKmurL9LkvXtyk0kVrS8G9wk=", "__NEXT_PREVIEW_MODE_ID": "50535d62593c0bb1a6865aa834b26a1d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "542762a14fa62897557476247b747429c4a1b137e7b82a7b6c26e5d2030bf96d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ca3e32fb0cfc17d636a860fb845eb4b67fd6ea503f9803b170ee331f281df642"}}}, "functions": {}, "sortedMiddleware": ["/"]}